'use client';

import { NavLink, Stack, ThemeIcon } from '@mantine/core';
import {
  IconDashboard,
  IconUser,
  IconSearch,
  IconLogout,
} from '@tabler/icons-react';
import { useRouter, usePathname } from 'next/navigation';
import { useAccount, useDisconnect } from 'wagmi';

const navigationItems = [
  { icon: IconDashboard, label: 'Dashboard', href: '/dashboard' },
  { icon: IconUser, label: 'My Names', href: '/my-names' },
  { icon: IconSearch, label: 'Search', href: '/search' },
];

export function Navigation() {
  const router = useRouter();
  const pathname = usePathname();
  const { isConnected } = useAccount();
  const { disconnect } = useDisconnect();

  const handleNavigation = (href: string) => {
    router.push(href);
  };

  const handleDisconnect = () => {
    disconnect();
    router.push('/');
  };

  return (
    <Stack gap="xs">
      {navigationItems.map((item) => (
        <NavLink
          key={item.href}
          active={pathname === item.href}
          label={item.label}
          leftSection={
            <ThemeIcon variant="light" size={30}>
              <item.icon size={18} />
            </ThemeIcon>
          }
          onClick={() => handleNavigation(item.href)}
          style={{ borderRadius: '8px' }}
        />
      ))}

      {isConnected && (
        <div style={{ marginTop: 'auto', paddingTop: '20px' }}>
          <NavLink
            label="Disconnect Wallet"
            leftSection={
              <ThemeIcon variant="light" color="red" size={30}>
                <IconLogout size={18} />
              </ThemeIcon>
            }
            onClick={handleDisconnect}
            style={{ borderRadius: '8px' }}
          />
        </div>
      )}
    </Stack>
  );
}
