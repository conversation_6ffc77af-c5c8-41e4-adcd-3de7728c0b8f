'use client';

import { useEffect, useState, useCallback } from 'react';
import { useRouter } from 'next/navigation';
import { useAccount } from 'wagmi';
import {
  Container,
  Title,
  Text,
  Stack,
  Paper,
  Group,
  ThemeIcon,
  Loader,
  Alert,
  SimpleGrid,
  Card,
  Badge,
  Button,
  Center,
  Divider,
} from '@mantine/core';
import {
  IconUser,
  IconAlertCircle,
  IconWallet,
  IconExternalLink,
  IconCopy,
  IconRefresh,
} from '@tabler/icons-react';
import { AppShellLayout } from '@/components/Layout/AppShell';
import { initializeSDK, sdk } from '@/lib/odude';
import { useClipboard } from '@mantine/hooks';
import { notifications } from '@mantine/notifications';

interface NameItem {
  tokenId: string;
  name: string;
  owner?: string;
  resolvedAddress?: string;
  metadata?: Record<string, unknown>;
}

export default function MyNames() {
  const { address, isConnected } = useAccount();
  const router = useRouter();
  const clipboard = useClipboard({ timeout: 500 });
  
  const [loading, setLoading] = useState(true);
  const [error, setError] = useState<string | null>(null);
  const [names, setNames] = useState<NameItem[]>([]);
  const [totalNames, setTotalNames] = useState<number>(0);
  const [sdkReady, setSdkReady] = useState(false);

  useEffect(() => {
    if (!isConnected) {
      router.push('/');
      return;
    }

    initializeSDK()
      .then(() => setSdkReady(true))
      .catch((err) => setError(err.message));
  }, [isConnected, router]);

  useEffect(() => {
    if (sdkReady && address) {
      fetchNames();
    }
  }, [sdkReady, address, fetchNames]);

  const fetchNames = useCallback(async () => {
    if (!address) return;
    
    setLoading(true);
    setError(null);
    
    try {
      // Get total names count
      const total = await sdk.getTotalNames(address);
      setTotalNames(Number(total));
      
      if (total === 0n) {
        setNames([]);
        setLoading(false);
        return;
      }
      
      // Get names list
      const namesList = await sdk.getNamesList(address);
      
      // Get detailed information for each name
      const namesWithDetails = await Promise.all(
        namesList.map(async (item) => {
          try {
            const details = await sdk.getNameDetails(item.name);
            return {
              tokenId: item.tokenId,
              name: item.name,
              owner: details.owner,
              resolvedAddress: details.resolvedAddress,
              metadata: details.metadata,
            };
          } catch (err) {
            console.error(`Failed to get details for ${item.name}:`, err);
            return {
              tokenId: item.tokenId,
              name: item.name,
            };
          }
        })
      );
      
      setNames(namesWithDetails);
    } catch (err) {
      console.error('Failed to fetch names:', err);
      setError(err instanceof Error ? err.message : 'Failed to fetch names');
    } finally {
      setLoading(false);
    }
  }, [address]);

  const handleCopyAddress = (addr: string) => {
    clipboard.copy(addr);
    notifications.show({
      title: 'Address Copied',
      message: 'Address copied to clipboard',
      color: 'green',
    });
  };

  const formatAddress = (addr: string) => {
    return `${addr.slice(0, 6)}...${addr.slice(-4)}`;
  };

  if (!isConnected) {
    return null; // Will redirect
  }

  return (
    <AppShellLayout>
      <Container size="xl">
        <Stack gap="xl">
          <Group>
            <ThemeIcon size={40} radius="xl" variant="gradient" gradient={{ from: 'blue', to: 'purple' }}>
              <IconUser size={24} />
            </ThemeIcon>
            <div>
              <Title order={1}>My Names</Title>
              <Text c="dimmed">Manage your ODude domain names</Text>
            </div>
            <Button
              leftSection={<IconRefresh size={16} />}
              variant="outline"
              onClick={fetchNames}
              loading={loading}
              ml="auto"
            >
              Refresh
            </Button>
          </Group>

          {/* Summary */}
          <Paper shadow="sm" p="lg" radius="md" withBorder>
            <Group justify="space-between">
              <div>
                <Text size="sm" c="dimmed">Total Names Owned</Text>
                <Text size="xl" fw={700}>{totalNames}</Text>
              </div>
              <div>
                <Text size="sm" c="dimmed">Wallet Address</Text>
                <Group gap="xs">
                  <Text size="sm" fw={500}>{formatAddress(address!)}</Text>
                  <Button
                    size="xs"
                    variant="subtle"
                    onClick={() => handleCopyAddress(address!)}
                  >
                    <IconCopy size={14} />
                  </Button>
                </Group>
              </div>
            </Group>
          </Paper>

          {/* Error State */}
          {error && (
            <Alert icon={<IconAlertCircle size={16} />} title="Error" c="red">
              {error}
            </Alert>
          )}

          {/* Loading State */}
          {loading && (
            <Center py="xl">
              <Stack align="center" gap="md">
                <Loader size="lg" />
                <Text c="dimmed">Loading your names...</Text>
              </Stack>
            </Center>
          )}

          {/* Empty State */}
          {!loading && !error && names.length === 0 && (
            <Paper shadow="sm" p="xl" radius="md" withBorder>
              <Center>
                <Stack align="center" gap="md">
                  <ThemeIcon size={60} radius="xl" c="gray">
                    <IconWallet size={30} />
                  </ThemeIcon>
                  <div style={{ textAlign: 'center' }}>
                    <Text size="lg" fw={500}>No Names Found</Text>
                    <Text c="dimmed" mt="xs">
                      You don&apos;t own any ODude names yet. Start by searching for available names!
                    </Text>
                  </div>
                  <Button
                    variant="gradient"
                    gradient={{ from: 'blue', to: 'cyan' }}
                    onClick={() => router.push('/search')}
                  >
                    Search Names
                  </Button>
                </Stack>
              </Center>
            </Paper>
          )}

          {/* Names Grid */}
          {!loading && !error && names.length > 0 && (
            <SimpleGrid cols={2} breakpoints={[{ maxWidth: 'md', cols: 1 }]}>
              {names.map((nameItem) => (
                <Card key={nameItem.tokenId} shadow="sm" p="lg" radius="md" withBorder>
                  <Stack gap="md">
                    <Group justify="space-between">
                      <div>
                        <Text fw={600} size="lg">{nameItem.name}</Text>
                        <Text size="sm" c="dimmed">Token ID: {nameItem.tokenId}</Text>
                      </div>
                      <Badge variant="light" color="blue">
                        Owned
                      </Badge>
                    </Group>

                    <Divider />

                    <Stack gap="xs">
                      {nameItem.resolvedAddress && (
                        <Group justify="space-between">
                          <Text size="sm" c="dimmed">Resolves to:</Text>
                          <Group gap="xs">
                            <Text size="sm" fw={500}>
                              {formatAddress(nameItem.resolvedAddress)}
                            </Text>
                            <Button
                              size="xs"
                              variant="subtle"
                              onClick={() => handleCopyAddress(nameItem.resolvedAddress!)}
                            >
                              <IconCopy size={12} />
                            </Button>
                          </Group>
                        </Group>
                      )}

                      {nameItem.owner && (
                        <Group justify="space-between">
                          <Text size="sm" c="dimmed">Owner:</Text>
                          <Group gap="xs">
                            <Text size="sm" fw={500}>
                              {formatAddress(nameItem.owner)}
                            </Text>
                            <Button
                              size="xs"
                              variant="subtle"
                              onClick={() => handleCopyAddress(nameItem.owner!)}
                            >
                              <IconCopy size={12} />
                            </Button>
                          </Group>
                        </Group>
                      )}

                      {nameItem.metadata && (
                        <>
                          {nameItem.metadata.description && (
                            <div>
                              <Text size="sm" c="dimmed">Description:</Text>
                              <Text size="sm">{nameItem.metadata.description}</Text>
                            </div>
                          )}
                        </>
                      )}
                    </Stack>

                    <Group justify="flex-end" mt="md">
                      <Button
                        size="xs"
                        variant="outline"
                        leftSection={<IconExternalLink size={14} />}
                        onClick={() => {
                          router.push(`/info/${encodeURIComponent(nameItem.name)}`);
                        }}
                      >
                        View Details
                      </Button>
                    </Group>
                  </Stack>
                </Card>
              ))}
            </SimpleGrid>
          )}
        </Stack>
      </Container>
    </AppShellLayout>
  );
}
